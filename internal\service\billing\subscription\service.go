package subscription

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"io"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/kiwify"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/invoice"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/product"
	"github.com/dsoplabs/dinbora-backend/internal/service/notification/sendgrid"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Core
	Create(ctx context.Context, user string, params billing.Subscription) (*billing.SubscriptionParams, error)
	Subscribe(ctx context.Context, customer string, invoice string) error
	SyncAll(ctx context.Context, customer string) error
	Cancel(ctx context.Context, id string, customer string) error
	PaymentIntendCanceled(ctx context.Context, customer string, invoice string) error

	// External Integrations
	SubscribeKiwify(context.Context, kiwify.Order) error
	CancelContract(ctx context.Context, id string, customerExternalCode string) error
	CancelContractKiwify(ctx context.Context, id string, email string) error
	//SubscribeAppStore(context.Context, string, billing.AppStore) error
	//CancelContractAppStore(ctx context.Context, id string, userId string) error
}

// Constants
const (
	ArkcPremiumIdentifier = "arkc-premium"
	ArkcBlackId           = "62a59c5c9057732b767ab5ae"
	BeginnerPlan          = "62a599a39057732b767ab5ac"
	PremiumPlan           = "62a59b629057732b767ab5ad"
	BlackPlan             = "62a59c5c9057732b767ab5ae"
)

type service struct {
	UserService     user.Service
	ContractService contract.Service
	ProductService  product.Service
	InvoiceService  invoice.Service
	Sendgrid        sendgrid.Service
}

func New(userService user.Service, contractService contract.Service, productService product.Service, invoiceService invoice.Service, sendgrid sendgrid.Service) Service {
	return &service{
		UserService:     userService,
		ContractService: contractService,
		ProductService:  productService,
		InvoiceService:  invoiceService,
		Sendgrid:        sendgrid,
	}
}

// Core
func (s *service) Create(ctx context.Context, user string, params billing.Subscription) (*billing.SubscriptionParams, error) {
	// Stripe functionality removed - subscription creation no longer supported
	return nil, errors.New(errors.Service, "Stripe subscription functionality has been removed", errors.NotImplemented, nil)
}

func (s *service) Subscribe(ctx context.Context, customer string, invoice string) error {
	foundUser, err := s.UserService.FindByExternalCode(ctx, customer)
	if err != nil {
		return err
	}

	subscriptionInvoice, err := s.InvoiceService.Sync(ctx, invoice, "")
	if err != nil {
		return err
	}

	plan, err := s.ProductService.FindByIdentifier(ctx, subscriptionInvoice.Plan)
	if err != nil {
		return err
	}

	err = s.ContractService.Subscribe(ctx, &billing.SubscriptionData{
		Customer:     foundUser,
		Plan:         plan.ID,
		Invoice:      subscriptionInvoice,
		Status:       billing.ACTIVE,
		ExternalCode: subscriptionInvoice.ContractExternalCode,
	})

	if err != nil {
		return err
	}

	err = s.SyncAll(ctx, foundUser.ExternalCode)
	if err != nil {
		return err
	}

	_, _ = s.Sendgrid.SendSubscribeEmail(foundUser, plan.ID)

	return nil
}

func (s *service) SyncAll(ctx context.Context, customer string) error {
	// Stripe functionality removed - sync no longer supported
	return nil
}

func (s *service) Cancel(ctx context.Context, id string, customer string) error {
	// Stripe functionality removed - cancellation no longer supported
	return errors.New(errors.Service, "Stripe subscription functionality has been removed", errors.NotImplemented, nil)
}

func (s *service) PaymentIntendCanceled(ctx context.Context, customer string, invoice string) error {
	foundUser, err := s.UserService.FindByExternalCode(ctx, customer)
	if err != nil {
		return err
	}

	subscriptionInvoice, err := s.InvoiceService.Sync(ctx, invoice, "")
	if err != nil {
		return err
	}

	plan, err := s.ProductService.FindByIdentifier(ctx, subscriptionInvoice.Plan)
	if err != nil {
		return err
	}

	// Subscribe the customer but with a canceled status
	err = s.ContractService.PaymentIntendCanceled(ctx, &billing.SubscriptionData{
		Customer:     foundUser,
		Plan:         plan.ID,
		Invoice:      subscriptionInvoice,
		Status:       billing.CANCELLED,
		ExternalCode: subscriptionInvoice.ContractExternalCode,
	})

	if err != nil {
		return err
	}

	err = s.SyncAll(ctx, foundUser.ExternalCode)
	if err != nil {
		return err
	}

	return nil
}

// External Integrations
func (s *service) SubscribeKiwify(ctx context.Context, kiwify kiwify.Order) error {
	// Check if the customer is in our platform.
	foundCustomer, err := s.UserService.FindByEmail(ctx, kiwify.Customer.Email)
	var userFound bool
	if err != nil {
		// If user not found create the user in the platform.
		userFound = false
		// For testing
		var tmp_password string
		if os.Getenv("APP_URL") != "https://dinbora.com.br" {
			tmp_password = "Mudar@123"
		} else {
			tmp_password = randomStringGenerator(10) + "@" + randomStringGenerator(10)
		}
		newUser := &model.User{
			Name:     kiwify.Customer.FullName,
			Email:    kiwify.Customer.Email,
			Password: tmp_password,
			Phone:    kiwify.Customer.Mobile,
		}

		if err := newUser.PrepareCreate(); err != nil {
			return err
		}

		if err := s.UserService.Create(ctx, newUser, ""); err != nil {
			return err
		}

		// Search again in database to secure user creation (was buggy before due to user unique ID)
		// Suggested future fix is to wait mongodb response regarding the creation of a user and than create contract linking to the user.
		foundCustomer, err = s.UserService.FindByEmail(ctx, kiwify.Customer.Email)
		if err != nil {
			return err
		}
	} else {
		userFound = true
	}

	foundPlan, err := s.ProductService.FindByName(ctx, kiwify.Product.ProductName)
	if err != nil {
		return err
	}

	err = s.ContractService.SubscribeKiwify(ctx, &billing.SubscriptionData{
		Customer:         foundCustomer,
		AutomaticRenewal: true,
		Plan:             foundPlan.ID,
		Status:           billing.ACTIVE,
		ExternalCode:     kiwify.SubscriptionID,
	}, &kiwify)

	if err != nil {
		return err
	}

	// Send different e-mails in case user is already registered in the platform.
	if userFound {
		_, _ = s.Sendgrid.SendSubscribeEmail(foundCustomer, foundPlan.ID)
	} else {
		_, _ = s.Sendgrid.SendSubscribeEmailNewUser(foundCustomer, foundPlan.ID)
	}

	return nil
}

func (s *service) CancelContract(ctx context.Context, id string, customerExternalCode string) error {
	foundCustomer, err := s.UserService.FindByExternalCode(ctx, customerExternalCode)
	if err != nil {
		foundDeletedCustomer, err := s.UserService.FindDeletedByExternalCode(ctx, customerExternalCode)
		if err != nil {
			return err
		}
		foundCustomer = foundDeletedCustomer
	}

	foundContract, err := s.ContractService.FindByExternalCode(ctx, id, foundCustomer.ID)
	if err != nil {
		return err
	}
	// [TODO]: After testing, add same implementation of Kiwify to avoid code duplication.
	if foundContract != nil {
		if foundContract.Status != billing.CANCELLED {
			_, _ = s.Sendgrid.SendCancelEmail(foundCustomer, foundContract.Plan)
			return s.ContractService.Cancel(ctx, foundContract.ID, foundCustomer.ID)
		} else {
			return err
		}
	}
	return err
}

func (s *service) CancelContractKiwify(ctx context.Context, id string, email string) error {
	foundCustomer, err := s.UserService.FindByEmail(ctx, email)
	if err != nil {
		foundDeletedCustomer, err := s.UserService.FindDeletedByEmail(ctx, email)
		if err != nil {
			return err
		}
		foundCustomer = foundDeletedCustomer
	}

	foundContract, err := s.ContractService.FindByExternalCode(ctx, id, foundCustomer.ID)
	if err != nil {
		return err
	}

	if foundContract != nil {
		return s.sendCancelEmail(ctx, foundCustomer, foundContract)
	}
	return err
}

// Helper
func (s *service) sendCancelEmail(ctx context.Context, foundCustomer *model.User, foundContract *billing.Contract) error {
	if foundContract.Status != billing.CANCELLED {
		_, _ = s.Sendgrid.SendCancelEmail(foundCustomer, foundContract.Plan)
		return s.ContractService.Cancel(ctx, foundContract.ID, foundCustomer.ID)
	} else {
		return errors.OldError("contract already cancelled", errors.Conflict, nil)
	}
}

func getContractStatusBySubscriptionStatus(status stripe.SubscriptionStatus) billing.Status {
	switch status {
	case stripe.SubscriptionStatusActive:
		return billing.ACTIVE
	case stripe.SubscriptionStatusCanceled:
		return billing.CANCELLED
	case stripe.SubscriptionStatusIncomplete:
		return billing.NOT_COMPLETED
	case stripe.SubscriptionStatusIncompleteExpired:
		return billing.CANCELLED
	case stripe.SubscriptionStatusPastDue:
		return billing.DISABLED
	case stripe.SubscriptionStatusTrialing:
		return billing.ACTIVE
	case stripe.SubscriptionStatusUnpaid:
		return billing.ACTIVE
	default:
		return billing.DISABLED
	}
}

func randomStringGenerator(n int) string {
	data := make([]byte, n)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(data)
}

// Temporarily removed AppStore methods
// func (s *service) SubscribeAppStore...
// func (s *service) CancelContractAppStore...
