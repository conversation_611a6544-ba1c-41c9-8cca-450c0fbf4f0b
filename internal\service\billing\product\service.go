package product

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/billing/product"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// Product CRUD
	Create(ctx context.Context, product *billing.Product) error
	Find(ctx context.Context, id string) (*billing.Product, error)
	FindMany(ctx context.Context, ids []string) (map[string]*billing.Product, error)
	FindAll(ctx context.Context) ([]*billing.Product, error)
	FindByIdentifier(ctx context.Context, identifier string) (*billing.Product, error)
	FindByName(ctx context.Context, name string) (*billing.Product, error)
	Update(ctx context.Context, product *billing.Product) error
	Delete(ctx context.Context, id string) error

	// Sync
	SyncAll(ctx context.Context) error
	Sync(ctx context.Context, product *billing.Product) error

	// Utility
	FindByPricingExternalCode(ctx context.Context, price string) (*billing.Product, error)
}

type service struct {
	Repository product.Repository
}

func New(repository product.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, product *billing.Product) error {
	foundProduct, err := s.Repository.FindByIdentifier(ctx, product.Identifier)
	if err == nil && foundProduct != nil {
		return err
	}

	if err = s.Repository.Create(ctx, product); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*billing.Product, error) {
	foundProduct, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundProduct.ID = foundProduct.ObjectID.Hex()
	return foundProduct, nil
}

func (s *service) FindMany(ctx context.Context, ids []string) (map[string]*billing.Product, error) {
	if len(ids) == 0 {
		return map[string]*billing.Product{}, nil
	}

	objectIDs := make([]primitive.ObjectID, 0, len(ids))
	for _, id := range ids {
		objID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			// Consider how to handle invalid IDs - skip, error out, etc.
			// For now, returning an error for any invalid ID.
			return nil, errors.New(errors.Service, "invalid product ID format in FindMany", errors.Validation, err)
		}
		objectIDs = append(objectIDs, objID)
	}

	products, err := s.Repository.FindMany(ctx, objectIDs)
	if err != nil {
		return nil, err // Propagate repository error
	}

	productMap := make(map[string]*billing.Product, len(products))
	for _, p := range products {
		p.ID = p.ObjectID.Hex() // Ensure string ID is set
		productMap[p.ID] = p
	}

	return productMap, nil
}

func (s *service) FindAll(ctx context.Context) ([]*billing.Product, error) {
	foundProducts, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	if foundProducts != nil {
		for _, p := range foundProducts {
			p.ID = p.ObjectID.Hex()
		}
	}

	return foundProducts, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*billing.Product, error) {
	foundProduct, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundProduct.ID = foundProduct.ObjectID.Hex()
	return foundProduct, nil
}

func (s *service) FindByName(ctx context.Context, name string) (*billing.Product, error) {
	foundProduct, err := s.Repository.FindByName(ctx, name)
	if err != nil {
		return nil, err
	}
	foundProduct.ID = foundProduct.ObjectID.Hex()
	return foundProduct, nil
}

func (s *service) Update(ctx context.Context, product *billing.Product) error {
	return s.Repository.Update(ctx, product)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}

// Sync
func (s *service) SyncAll(ctx context.Context) error {
	// Stripe functionality removed - this method now does nothing
	return nil
}

func (s *service) Sync(ctx context.Context, product *billing.Product) error {
	// Stripe functionality removed - this method now does nothing
	return nil
}

// Utility
func (s *service) FindByPricingExternalCode(ctx context.Context, price string) (*billing.Product, error) {
	foundProducts, err := s.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, foundProduct := range foundProducts {
		if foundProduct.Pricing.ExternalCode == price {
			return foundProduct, nil
		}
	}

	return nil, errors.New(errors.Service, "product not found by external code", errors.NotFound, err)
}
