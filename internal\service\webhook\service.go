package webhook

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/webhook"
	"github.com/dsoplabs/dinbora-backend/pkg"
)

type Service interface {
	// Webhook CRUD
	Create(ctx context.Context, webhook *model.DinboraWebhook) error
	Find(ctx context.Context, id string) (*model.DinboraWebhook, error)
	FindByUrl(ctx context.Context, url string) (*model.DinboraWebhook, error)
	Update(ctx context.Context, webhook *model.DinboraWebhook) error
	Delete(ctx context.Context, id string) error

	// Sync
	Sync(ctx context.Context, webhook *model.DinboraWebhook) (*model.DinboraWebhook, error)
}

type service struct {
	Repository webhook.Repository
}

func New(repository webhook.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, webhook *model.DinboraWebhook) error {
	foundProduct, err := s.Repository.FindByUrl(ctx, webhook.Url)
	if err == nil && foundProduct != nil {
		return err
	}

	if err = s.Repository.Create(ctx, webhook); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*model.DinboraWebhook, error) {
	foundWebhook, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundWebhook.ID = foundWebhook.ObjectID.Hex()
	return foundWebhook, nil
}

func (s *service) FindByUrl(ctx context.Context, url string) (*model.DinboraWebhook, error) {
	foundWebhook, err := s.Repository.FindByUrl(ctx, url)
	if err != nil {
		return nil, err
	}
	foundWebhook.ID = foundWebhook.ObjectID.Hex()
	return foundWebhook, nil
}

func (s *service) Update(ctx context.Context, webhook *model.DinboraWebhook) error {
	return s.Repository.Update(ctx, webhook)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}

// Sync
func (s *service) Sync(ctx context.Context, webhook *model.DinboraWebhook) (*model.DinboraWebhook, error) {
	foundDinboraWebhook, err := s.FindByUrl(ctx, webhook.Url)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
	}

	if foundDinboraWebhook != nil {
		return foundDinboraWebhook, nil
	} else {
		// Stripe functionality removed - generate a dummy secret
		webhook.Secret, err = pkg.Encrypt("dummy_secret")
		if err != nil {
			return nil, err
		}

		err = s.Create(ctx, webhook)
		if err != nil {
			return nil, err
		}
	}

	return s.FindByUrl(ctx, webhook.Url)
}
