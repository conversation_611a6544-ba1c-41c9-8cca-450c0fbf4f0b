package subscription

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/kiwify"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/subscription"
	"github.com/dsoplabs/dinbora-backend/internal/service/webhook"
	"github.com/dsoplabs/dinbora-backend/pkg"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Cancel() echo.HandlerFunc

	// Webhook
	Webhook(secret string) echo.HandlerFunc
}

type controller struct {
	Service        subscription.Service
	WebhookService webhook.Service
}

func New(service subscription.Service, webhookService webhook.Service) Controller {
	return &controller{
		Service:        service,
		WebhookService: webhookService,
	}
}

// Routes
func (ch *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	subscriptionGroup := legacyGroup.Group("billing/subscription/")

	// CRUD
	subscriptionGroup.POST("", ch.Create(), middlewares.AuthGuard())
	subscriptionGroup.POST("cancel/", ch.Cancel(), middlewares.AuthGuard())

	webhookEndpoint := &model.DinboraWebhook{
		Description: "Webhook for subscriptions ",
		Events: []string{
			"payment_intent.succeeded",
			"payment_intent.canceled",
			"customer.subscription.deleted",
		},
		Url: "https://www.dinbora.com.br/api/v1/billing/subscription/webhook/",
	}

	if os.Getenv("API_MODE") == "Test" {
		webhookEndpoint.Url = "https://api.dinbora.binaryaces.dev/v1/billing/subscription/webhook/"
	}

	if os.Getenv("API_MODE") == "Test-Hetzner" {
		webhookEndpoint.Url = "https://api.arkc.binaryaces.dev/v1/billing/subscription/webhook/"
	}

	wh, err := ch.WebhookService.Sync(context.TODO(), webhookEndpoint)
	if err != nil {
		log.Fatal(err)
	}

	var secret string
	if os.Getenv("API_URL") == "" {
		secret = os.Getenv("WEB_HOOK_KEY")
	} else {
		secret, err = pkg.Decrypt(wh.Secret)
		if err != nil {
			log.Fatal(err)
		}
	}

	// Webhook
	subscriptionGroup.POST("webhook/", ch.Webhook(secret))

	// Kiwify
	subscriptionGroup.POST("kiwify/webhook/", ch.KiwiFyWebhook())
}

// CRUD
func (ch *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		customerToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var input billing.Subscription

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		stripeSubscription, err := ch.Service.Create(ctx, customerToken.Uid, input)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, stripeSubscription)
	}
}

func (ch *controller) Cancel() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		customerToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var input billing.SubscriptionCancelParams

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		err = ch.Service.Cancel(ctx, input.Contract, customerToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Webhook - Stripe functionality removed
func (ch *controller) Webhook(secret string) echo.HandlerFunc {
	return func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{"message": "Stripe webhook functionality has been removed"})
	}
}

func (ch *controller) KiwiFyWebhook() echo.HandlerFunc {
	return func(c echo.Context) error {
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		var kiwify kiwify.Order
		if err := json.Unmarshal(body, &kiwify); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		log.Println("STATUS", kiwify.OrderStatus)
		log.Println("SUBSCRIPTION STATUS", kiwify.Subscription.Status)
		log.Println("SUBSCRIPTION ID", kiwify.SubscriptionID)

		signature := c.Request().URL.Query().Get("signature")

		if checkKiwifySignature(body, signature, os.Getenv("KIWIFY_WEB_HOOK_TOKEN")) {
			switch kiwify.OrderStatus {
			case "paid":
				switch kiwify.Subscription.Status {
				case "active":
					if err := ch.Service.SubscribeKiwify(context.Background(), kiwify); err != nil {
						return err
					}
				case "canceled":
					if err := ch.Service.CancelContractKiwify(context.Background(), kiwify.SubscriptionID, kiwify.Customer.Email); err != nil {
						return err
					}
				}
			case "refunded":
				if err := ch.Service.CancelContractKiwify(context.Background(), kiwify.SubscriptionID, kiwify.Customer.Email); err != nil {
					return err
				}
			default:
				return c.JSON(http.StatusOK, nil)
			}
		} else {
			return errors.New(errors.Controller, "kiwify not authorized webhook", errors.Validation, nil)
		}

		return c.JSON(http.StatusOK, nil)
	}
}

// Helper
func checkKiwifySignature(data []byte, receivedSignature, secret string) bool {
	hash := hmac.New(sha1.New, []byte(secret))
	hash.Write(data)
	calculatedSignature := hex.EncodeToString(hash.Sum(nil))
	return calculatedSignature == receivedSignature
}

/* Temporarily removed code for reference
func (ch *controller) CancelContract() echo.HandlerFunc {
return func(c echo.Context) error {
ctx := c.Request().Context()

customerToken, err := token.GetClaimsFromRequest(c.Request())
if err != nil {
return err
}

var input billing.SubscriptionCancelParams

if err := c.Bind(&input); err != nil {
return controllerPackage.ErrCannotProcessInput
}

err = ch.Service.CancelContractAppStore(ctx, input.Contract, customerToken.Uid)
if err != nil {
return err
}

return c.JSON(http.StatusNoContent, nil)
}
}

func (ch *controller) AppStore() echo.HandlerFunc {
return func(c echo.Context) error {
ctx := c.Request().Context()

loggedUser, err := token.GetClaimsFromRequest(c.Request())
if err != nil {
return err
}

var subscription billing.AppStore

if err := c.Bind(&subscription); err != nil {
return controllerPackage.ErrCannotProcessInput
}

err = ch.Service.SubscribeAppStore(ctx, loggedUser.Uid, subscription)
if err != nil {
return err
}

return c.JSON(http.StatusOK, "")
}
}
*/
