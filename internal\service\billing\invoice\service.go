package invoice

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/billing/invoice"
	"github.com/dsoplabs/dinbora-backend/pkg"
)

type Service interface {
	// Invoice CRUD
	Create(ctx context.Context, invoice *billing.Invoice) (string, error)
	Find(ctx context.Context, id string) (*billing.Invoice, error)
	FindAll(ctx context.Context) ([]*billing.Invoice, error)
	FindByContract(ctx context.Context, contract string) ([]*billing.Invoice, error)
	FindByExternalCode(ctx context.Context, externalCode string) (*billing.Invoice, error)
	Update(ctx context.Context, invoice *billing.Invoice) error
	Delete(ctx context.Context, id string) error

	// Sync
	SyncAll() error
	Sync(ctx context.Context, id string, secret string) (*billing.Invoice, error)
}

type service struct {
	Repository invoice.Repository
}

func New(repository invoice.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// Invoice CRUD
func (s *service) Create(ctx context.Context, invoice *billing.Invoice) (string, error) {
	secret, err := pkg.Encrypt(invoice.ClientSecret)
	if err != nil {
		return "", err
	}
	invoice.ClientSecret = secret
	invoiceID, err := s.Repository.Create(ctx, invoice)
	if err != nil {
		return "", err
	}

	return invoiceID, nil
}

func (s *service) Find(ctx context.Context, id string) (*billing.Invoice, error) {
	foundInvoice, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}

	foundInvoice.ID = foundInvoice.ObjectID.Hex()
	secret, err := pkg.Decrypt(foundInvoice.ClientSecret)
	if err != nil {
		return nil, err
	}

	foundInvoice.ClientSecret = secret

	return foundInvoice, nil
}

func (s *service) FindAll(ctx context.Context) ([]*billing.Invoice, error) {
	foundInvoices, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, p := range foundInvoices {
		p.ID = p.ObjectID.Hex()
		if p.ClientSecret != "" {
			secret, err := pkg.Decrypt(p.ClientSecret)
			if err != nil {
				return nil, err
			}
			p.ClientSecret = secret
		}
	}

	return foundInvoices, nil
}

func (s *service) FindByContract(ctx context.Context, contract string) ([]*billing.Invoice, error) {
	foundInvoices, err := s.Repository.FindByContract(ctx, contract)
	if err != nil {
		return nil, err
	}
	if foundInvoices != nil {
		for _, p := range foundInvoices {
			p.ID = p.ObjectID.Hex()
			if p.ClientSecret != "" {
				secret, err := pkg.Decrypt(p.ClientSecret)
				if err != nil {
					return nil, err
				}
				p.ClientSecret = secret
			}
		}
	}

	return foundInvoices, nil
}

func (s *service) FindByExternalCode(ctx context.Context, externalCode string) (*billing.Invoice, error) {
	foundInvoice, err := s.Repository.FindByExternalCode(ctx, externalCode)
	if err != nil {
		return nil, err
	}

	if foundInvoice.ClientSecret != "" {
		secret, err := pkg.Decrypt(foundInvoice.ClientSecret)
		if err != nil {
			return nil, err
		}
		foundInvoice.ClientSecret = secret
	}

	foundInvoice.ID = foundInvoice.ObjectID.Hex()
	return foundInvoice, nil
}

func (s *service) Update(ctx context.Context, invoice *billing.Invoice) error {
	secret, err := pkg.Encrypt(invoice.ClientSecret)
	if err != nil {
		return err
	}
	invoice.ClientSecret = secret
	return s.Repository.Update(ctx, invoice)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}

// Sync
func (s *service) SyncAll() error {
	return nil
}

func (s *service) Sync(ctx context.Context, id string, secret string) (*billing.Invoice, error) {
	// Stripe functionality removed - this method now only finds existing invoices
	foundInvoice, err := s.FindByExternalCode(ctx, id)
	if err != nil {
		return nil, err
	}

	return foundInvoice, nil
}
